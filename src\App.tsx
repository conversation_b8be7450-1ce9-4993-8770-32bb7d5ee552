import { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Navigation } from './components/layout/navigation';
import { Footer } from './components/layout/footer';
import { CommandPaletteProvider } from './hooks/use-command-palette';
import { CommandPaletteModal } from './components/layout/command-palette';
import { PerformanceMonitor } from './components/performance/performance-monitor';
import { ResourcePreloader } from './components/performance/resource-preloader';
import { WebVitalsMonitor } from './components/performance/web-vitals-monitor';

// Lazy load pages for better performance
const HomePage = lazy(() => import('./pages/home').then(module => ({ default: module.HomePage })));
const AboutPage = lazy(() => import('./pages/about').then(module => ({ default: module.AboutPage })));
const SkillsPage = lazy(() => import('./pages/skills').then(module => ({ default: module.SkillsPage })));
const ProjectsPage = lazy(() => import('./pages/projects').then(module => ({ default: module.ProjectsPage })));
const ContactPage = lazy(() => import('./pages/contact').then(module => ({ default: module.ContactPage })));

// Loading component for better UX
const PageLoader = () => (
  <div className="min-h-screen flex items-center justify-center bg-black">
    <div className="flex flex-col items-center space-y-4">
      <div className="w-8 h-8 border-2 border-white/20 border-t-white rounded-full animate-spin"></div>
      <p className="text-white/60 text-sm">Loading...</p>
    </div>
  </div>
);

function App() {
  return (
    <CommandPaletteProvider>
      <Router>
        <div className="min-h-screen bg-black text-white dark">
          <PerformanceMonitor />
          <ResourcePreloader />
          <WebVitalsMonitor />
          <Navigation />
          <Suspense fallback={<PageLoader />}>
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/about" element={<AboutPage />} />
              <Route path="/skills" element={<SkillsPage />} />
              <Route path="/projects" element={<ProjectsPage />} />
              <Route path="/contact" element={<ContactPage />} />
            </Routes>
          </Suspense>
          <Footer />
          <CommandPaletteModal />
        </div>
      </Router>
    </CommandPaletteProvider>
  );
}

export default App;