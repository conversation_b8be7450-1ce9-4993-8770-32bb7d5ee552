/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      screens: {
        'mobile': {'max': '425px'},
        'mobile': '425px',
        'sm-tablet': '640px',
        'tablet': '768px',
        'laptop-sm': '1024px',
        'laptop-md': '1280px',
        'laptop-lg': '1440px',
      },
      colors: {
        mono: {
          bg: 'var(--mono-bg)',
          text: 'var(--mono-text)',
          secondary: 'var(--mono-secondary)',
          border: 'var(--mono-border)',
          accent: 'var(--mono-accent)',
          'accent-light': 'var(--mono-accent-light)',
          'accent-dark': 'var(--mono-accent-dark)',
          surface: 'var(--mono-surface)',
          'surface-light': 'var(--mono-surface-light)',
          'surface-dark': 'var(--mono-surface-dark)',
        },
        accent: {
          neon: 'var(--accent-neon)',
          electric: 'var(--accent-electric)',
          vivid: 'var(--accent-vivid)',
        }
      },
      animation: {
        'fade-in': 'fade-in 0.5s ease-in-out',
        'fade-out': 'fade-out 0.5s ease-in-out',
        'slide-in': 'slide-in 0.5s ease-in-out',
        'slide-out': 'slide-out 0.5s ease-in-out',
      },
      keyframes: {
        'fade-in': {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        'fade-out': {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
        'slide-in': {
          '0%': { transform: 'translateY(-100%)' },
          '100%': { transform: 'translateY(0)' },
        },
        'slide-out': {
          '0%': { transform: 'translateY(0)' },
          '100%': { transform: 'translateY(-100%)' },
        },
      },
      fontFamily: {
        cursive: ['Playfair Display', 'serif'],
      },
    },
  },
  plugins: [],
}