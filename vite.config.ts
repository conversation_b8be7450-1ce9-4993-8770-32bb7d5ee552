import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'framer-motion',
      'lucide-react',
      'clsx',
      'tailwind-merge'
    ],
    exclude: ['cobe']
  },
  build: {
    sourcemap: false,
    cssCodeSplit: true,
    minify: 'esbuild',
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Vendor chunks
          if (id.includes('node_modules')) {
            if (id.includes('react') || id.includes('react-dom')) {
              return 'react-vendor'
            }
            if (id.includes('react-router-dom')) {
              return 'router'
            }
            if (id.includes('framer-motion')) {
              return 'motion'
            }
            if (id.includes('lucide-react') || id.includes('react-icons')) {
              return 'icons'
            }
            if (id.includes('@radix-ui') || id.includes('class-variance-authority') ||
                id.includes('clsx') || id.includes('tailwind-merge')) {
              return 'ui-utils'
            }
            if (id.includes('@emailjs/browser')) {
              return 'emailjs'
            }
            if (id.includes('react-use-measure')) {
              return 'utils'
            }
            // Other vendor libraries
            return 'vendor'
          }

          // Route-based code splitting
          if (id.includes('/pages/') || id.includes('/routes/')) {
            if (id.includes('home') || id.includes('Home')) {
              return 'page-home'
            }
            if (id.includes('about') || id.includes('About')) {
              return 'page-about'
            }
            if (id.includes('projects') || id.includes('Projects')) {
              return 'page-projects'
            }
            if (id.includes('contact') || id.includes('Contact')) {
              return 'page-contact'
            }
          }

          // Component-based splitting
          if (id.includes('/sections/')) {
            if (id.includes('home/')) {
              return 'sections-home'
            }
            if (id.includes('about/')) {
              return 'sections-about'
            }
            if (id.includes('projects/')) {
              return 'sections-projects'
            }
          }

          // UI components
          if (id.includes('/components/ui/')) {
            return 'ui-components'
          }
        }
      }
    },
    chunkSizeWarningLimit: 500, // Reduced for better performance
    assetsInlineLimit: 2048 // Reduced to prevent large inline assets
  }
});