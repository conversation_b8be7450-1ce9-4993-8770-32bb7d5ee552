// Test script to verify crawler access to your pages
// Run this after deployment to ensure Google can access your pages

const testUrls = [
  'https://cjjutba.site/',
  'https://cjjutba.site/about',
  'https://cjjutba.site/skills',
  'https://cjjutba.site/projects',
  'https://cjjutba.site/contact'
];

const userAgents = [
  'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
  'Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/W.X.Y.Z Mobile Safari/537.36 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
];

async function testUrl(url, userAgent) {
  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent': userAgent
      }
    });
    
    console.log(`${url} - ${userAgent.includes('Googlebot') ? 'Googlebot' : 'Browser'}: ${response.status}`);
    
    if (response.status !== 200) {
      console.error(`❌ FAILED: ${url} returned ${response.status}`);
      return false;
    }
    
    const text = await response.text();
    if (text.includes('404') || text.includes('Not Found')) {
      console.error(`❌ FAILED: ${url} contains 404 content`);
      return false;
    }
    
    console.log(`✅ SUCCESS: ${url} is accessible`);
    return true;
  } catch (error) {
    console.error(`❌ ERROR: ${url} - ${error.message}`);
    return false;
  }
}

async function runTests() {
  console.log('🔍 Testing crawler access to all pages...\n');
  
  let allPassed = true;
  
  for (const url of testUrls) {
    console.log(`\nTesting: ${url}`);
    for (const userAgent of userAgents) {
      const passed = await testUrl(url, userAgent);
      if (!passed) allPassed = false;
      await new Promise(resolve => setTimeout(resolve, 100)); // Small delay
    }
  }
  
  console.log('\n' + '='.repeat(50));
  if (allPassed) {
    console.log('🎉 ALL TESTS PASSED! Your pages are accessible to crawlers.');
  } else {
    console.log('❌ SOME TESTS FAILED! Check the errors above.');
  }
}

// Run tests if this file is executed directly
if (typeof window === 'undefined') {
  runTests();
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testUrl, runTests };
}
