{"name": "cj-jutba-portfolio", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cobe": "^0.6.4", "framer-motion": "^12.23.0", "lucide-react": "^0.525.0", "motion": "^12.23.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-router-dom": "^7.6.3", "react-use-measure": "^2.1.7", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "sharp": "^0.34.3", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^7.0.2"}}