import { Suspense, lazy } from 'react';
import { HeroSection } from '@/components/sections/hero-section';
import { SEOHead, seoConfigs } from '@/components/seo/seo-head';

// Lazy load heavy sections for better performance
const FeaturedProjectsSection = lazy(() => import('@/components/sections/home/<USER>').then(module => ({ default: module.FeaturedProjectsSection })));
const TechStackSection = lazy(() => import('@/components/sections/home/<USER>').then(module => ({ default: module.TechStackSection })));
const FlowingRibbon = lazy(() => import('@/components/ui/flowing-ribbon'));
const LearningGrowthSection = lazy(() => import('@/components/sections/home/<USER>').then(module => ({ default: module.LearningGrowthSection })));
const PersonalStorySection = lazy(() => import('@/components/sections/home/<USER>').then(module => ({ default: module.PersonalStorySection })));
const ContactPreviewSection = lazy(() => import('@/components/sections/home/<USER>').then(module => ({ default: module.ContactPreviewSection })));

// Loading component for sections
const SectionLoader = () => (
  <div className="w-full h-32 flex items-center justify-center bg-mono-bg">
    <div className="w-6 h-6 border-2 border-mono-accent/20 border-t-mono-accent rounded-full animate-spin"></div>
  </div>
);

export function HomePage() {
  return (
    <>
      <SEOHead {...seoConfigs.home} />
      <main className="min-h-screen bg-mono-bg">
        <HeroSection />

        <Suspense fallback={<SectionLoader />}>
          <FeaturedProjectsSection />
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <TechStackSection />
        </Suspense>

        {/* Slanted flowing ribbon separator */}
        <Suspense fallback={<SectionLoader />}>
          <FlowingRibbon />
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <LearningGrowthSection />
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <PersonalStorySection />
        </Suspense>

        <Suspense fallback={<SectionLoader />}>
          <ContactPreviewSection />
        </Suspense>
      </main>
    </>
  );
}