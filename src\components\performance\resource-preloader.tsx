'use client'
import { useEffect } from 'react'

interface ResourcePreloaderProps {
  criticalImages?: string[]
  prefetchImages?: string[]
  preloadFonts?: string[]
}

export function ResourcePreloader({ 
  criticalImages = ['/profile.png', '/projects/nexustore.png'],
  prefetchImages = ['/projects/portfolio-website.png', '/projects/taskflow.png'],
  preloadFonts = []
}: ResourcePreloaderProps) {
  useEffect(() => {
    // Preload critical images for LCP optimization
    criticalImages.forEach(src => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'image'
      link.href = src
      link.fetchPriority = 'high'
      document.head.appendChild(link)
    })

    // Prefetch non-critical images
    prefetchImages.forEach(src => {
      const link = document.createElement('link')
      link.rel = 'prefetch'
      link.as = 'image'
      link.href = src
      document.head.appendChild(link)
    })

    // Preload fonts
    preloadFonts.forEach(src => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'font'
      link.href = src
      link.crossOrigin = 'anonymous'
      document.head.appendChild(link)
    })

    // Preload critical CSS chunks
    const preloadCSS = () => {
      const criticalCSS = [
        '/src/index.css'
      ]
      
      criticalCSS.forEach(href => {
        const link = document.createElement('link')
        link.rel = 'preload'
        link.as = 'style'
        link.href = href
        document.head.appendChild(link)
      })
    }

    preloadCSS()

    // Optimize third-party resources
    const optimizeThirdParty = () => {
      // Defer non-critical scripts
      const scripts = document.querySelectorAll('script[src]')
      scripts.forEach(script => {
        if (!script.hasAttribute('async') && !script.hasAttribute('defer')) {
          script.setAttribute('defer', '')
        }
      })
    }

    optimizeThirdParty()

    // Cleanup function
    return () => {
      // Remove preload links after they're no longer needed
      const preloadLinks = document.querySelectorAll('link[rel="preload"]')
      preloadLinks.forEach(link => {
        if (link.getAttribute('as') === 'image') {
          // Keep image preloads as they might still be useful
          return
        }
        // Remove other preloads after a delay
        setTimeout(() => {
          if (link.parentNode) {
            link.parentNode.removeChild(link)
          }
        }, 5000)
      })
    }
  }, [criticalImages, prefetchImages, preloadFonts])

  return null // This component doesn't render anything
}

// Hook for component-level resource optimization
export function useResourceOptimization() {
  useEffect(() => {
    // Optimize images that are already loaded
    const optimizeLoadedImages = () => {
      const images = document.querySelectorAll('img')
      images.forEach(img => {
        // Add loading optimization attributes
        if (!img.hasAttribute('loading')) {
          img.setAttribute('loading', 'lazy')
        }
        if (!img.hasAttribute('decoding')) {
          img.setAttribute('decoding', 'async')
        }
      })
    }

    // Run optimization after a short delay
    setTimeout(optimizeLoadedImages, 1000)

    // Optimize animations based on device capabilities
    const optimizeAnimations = () => {
      const isLowEndDevice = 
        (navigator as any).deviceMemory && (navigator as any).deviceMemory < 4 ||
        (navigator as any).connection && (navigator as any).connection.effectiveType === 'slow-2g'

      if (isLowEndDevice) {
        // Disable heavy animations on low-end devices
        document.documentElement.style.setProperty('--animation-duration', '0.1s')
        document.documentElement.style.setProperty('--transition-duration', '0.1s')
      }
    }

    optimizeAnimations()

    // Monitor performance and adjust
    const monitorPerformance = () => {
      if ('requestIdleCallback' in window) {
        requestIdleCallback(() => {
          // Check if we're running slow
          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
          if (navigation && navigation.loadEventEnd - navigation.loadEventStart > 3000) {
            // Page is loading slowly, reduce animations
            document.documentElement.classList.add('reduce-animations')
          }
        })
      }
    }

    monitorPerformance()
  }, [])
}

// Critical CSS inlining utility
export function inlineCriticalCSS() {
  const criticalCSS = `
    /* Critical above-the-fold styles */
    body { 
      margin: 0; 
      font-family: Inter, system-ui, sans-serif; 
      background: #050505; 
      color: #ffffff; 
    }
    #root { 
      min-height: 100vh; 
      background: #050505; 
    }
    .hero-section { 
      min-height: 100vh; 
      display: flex; 
      align-items: center; 
      justify-content: center; 
    }
  `

  const style = document.createElement('style')
  style.textContent = criticalCSS
  document.head.appendChild(style)
}
