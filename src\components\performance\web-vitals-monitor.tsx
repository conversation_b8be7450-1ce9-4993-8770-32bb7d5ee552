'use client'
import { useEffect } from 'react'

interface WebVitalsMetrics {
  fcp?: number
  lcp?: number
  fid?: number
  cls?: number
  ttfb?: number
  inp?: number
}

export function WebVitalsMonitor() {
  useEffect(() => {
    // Only run in production for real user monitoring, and only if we have a real backend
    if (process.env.NODE_ENV !== 'production' || window.location.hostname === 'localhost') return

    const metrics: WebVitalsMetrics = {}

    // Measure First Contentful Paint (FCP)
    const measureFCP = () => {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint')
        if (fcpEntry) {
          metrics.fcp = fcpEntry.startTime
          reportMetric('FCP', fcpEntry.startTime)
          observer.disconnect()
        }
      })
      observer.observe({ entryTypes: ['paint'] })
    }

    // Measure Largest Contentful Paint (LCP)
    const measureLCP = () => {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        if (lastEntry) {
          metrics.lcp = lastEntry.startTime
          reportMetric('LCP', lastEntry.startTime)
        }
      })
      observer.observe({ entryTypes: ['largest-contentful-paint'] })
    }

    // Measure First Input Delay (FID)
    const measureFID = () => {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (entry.processingStart && entry.startTime) {
            const fid = entry.processingStart - entry.startTime
            metrics.fid = fid
            reportMetric('FID', fid)
          }
        })
      })
      observer.observe({ entryTypes: ['first-input'] })
    }

    // Measure Cumulative Layout Shift (CLS)
    const measureCLS = () => {
      let clsValue = 0
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
          }
        })
        metrics.cls = clsValue
        reportMetric('CLS', clsValue)
      })
      observer.observe({ entryTypes: ['layout-shift'] })
    }

    // Measure Time to First Byte (TTFB)
    const measureTTFB = () => {
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigationEntry) {
        const ttfb = navigationEntry.responseStart - navigationEntry.requestStart
        metrics.ttfb = ttfb
        reportMetric('TTFB', ttfb)
      }
    }

    // Measure Interaction to Next Paint (INP) - New Core Web Vital
    const measureINP = () => {
      if ('PerformanceEventTiming' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry: any) => {
            if (entry.processingStart && entry.startTime) {
              const inp = entry.processingStart - entry.startTime
              if (inp > (metrics.inp || 0)) {
                metrics.inp = inp
                reportMetric('INP', inp)
              }
            }
          })
        })
        observer.observe({ entryTypes: ['event'] })
      }
    }

    // Report metric to analytics (replace with your analytics service)
    const reportMetric = (name: string, value: number) => {
      // Example: Send to Google Analytics 4
      if (typeof gtag !== 'undefined') {
        gtag('event', 'web_vitals', {
          event_category: 'Web Vitals',
          event_label: name,
          value: Math.round(value),
          non_interaction: true
        })
      }

      // Example: Send to custom analytics endpoint (only in production with real backend)
      if (navigator.sendBeacon && process.env.NODE_ENV === 'production' && window.location.hostname !== 'localhost') {
        const data = JSON.stringify({
          metric: name,
          value: value,
          url: window.location.href,
          timestamp: Date.now(),
          userAgent: navigator.userAgent
        })

        navigator.sendBeacon('/api/analytics/web-vitals', data)
      }

      // Performance budget alerts
      checkPerformanceBudget(name, value)
    }

    // Performance budget thresholds
    const performanceBudgets = {
      FCP: 1800, // 1.8s
      LCP: 2500, // 2.5s
      FID: 100,  // 100ms
      CLS: 0.1,  // 0.1
      TTFB: 600, // 600ms
      INP: 200   // 200ms
    }

    // Check if metrics exceed performance budgets
    const checkPerformanceBudget = (metric: string, value: number) => {
      const budget = performanceBudgets[metric as keyof typeof performanceBudgets]
      if (budget && value > budget) {
        console.warn(`⚠️ Performance Budget Exceeded: ${metric} = ${value.toFixed(2)} (budget: ${budget})`)
        
        // Send alert to monitoring service (only in production with real backend)
        if (navigator.sendBeacon && process.env.NODE_ENV === 'production' && window.location.hostname !== 'localhost') {
          const alertData = JSON.stringify({
            type: 'performance_budget_exceeded',
            metric,
            value,
            budget,
            url: window.location.href,
            timestamp: Date.now()
          })
          navigator.sendBeacon('/api/alerts/performance', alertData)
        }
      }
    }

    // Initialize all measurements
    measureFCP()
    measureLCP()
    measureFID()
    measureCLS()
    measureTTFB()
    measureINP()

    // Report overall performance score after page load
    setTimeout(() => {
      const score = calculatePerformanceScore(metrics)
      reportMetric('Performance_Score', score)
    }, 3000)

    // Cleanup function
    return () => {
      // Performance observers are automatically cleaned up
    }
  }, [])

  return null // This component doesn't render anything
}

// Calculate overall performance score based on Core Web Vitals
function calculatePerformanceScore(metrics: WebVitalsMetrics): number {
  let score = 100

  // FCP scoring
  if (metrics.fcp) {
    if (metrics.fcp > 3000) score -= 25
    else if (metrics.fcp > 1800) score -= 15
  }

  // LCP scoring
  if (metrics.lcp) {
    if (metrics.lcp > 4000) score -= 30
    else if (metrics.lcp > 2500) score -= 20
  }

  // FID scoring
  if (metrics.fid) {
    if (metrics.fid > 300) score -= 25
    else if (metrics.fid > 100) score -= 15
  }

  // CLS scoring
  if (metrics.cls) {
    if (metrics.cls > 0.25) score -= 25
    else if (metrics.cls > 0.1) score -= 15
  }

  // TTFB scoring
  if (metrics.ttfb) {
    if (metrics.ttfb > 1500) score -= 15
    else if (metrics.ttfb > 600) score -= 10
  }

  return Math.max(0, score)
}

// Hook for component-level performance monitoring
export function useWebVitalsTracking(componentName: string) {
  useEffect(() => {
    if (process.env.NODE_ENV !== 'production') return

    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      // Track component render time
      if (navigator.sendBeacon) {
        const data = JSON.stringify({
          type: 'component_render_time',
          component: componentName,
          renderTime,
          timestamp: Date.now()
        })
        navigator.sendBeacon('/api/analytics/component-performance', data)
      }
    }
  }, [componentName])
}

// Performance monitoring for route changes
export function useRoutePerformance() {
  useEffect(() => {
    if (process.env.NODE_ENV !== 'production') return

    const handleRouteChange = () => {
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigationEntry) {
        const routeLoadTime = navigationEntry.loadEventEnd - navigationEntry.loadEventStart
        
        if (navigator.sendBeacon) {
          const data = JSON.stringify({
            type: 'route_load_time',
            url: window.location.href,
            loadTime: routeLoadTime,
            timestamp: Date.now()
          })
          navigator.sendBeacon('/api/analytics/route-performance', data)
        }
      }
    }

    // Listen for route changes (for SPA)
    window.addEventListener('popstate', handleRouteChange)
    
    return () => {
      window.removeEventListener('popstate', handleRouteChange)
    }
  }, [])
}
