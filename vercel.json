{"$schema": "https://openapi.vercel.sh/vercel.json", "headers": [{"source": "/sitemap.xml", "headers": [{"key": "Content-Type", "value": "application/xml"}, {"key": "Cache-Control", "value": "public, max-age=3600, s-maxage=3600"}]}, {"source": "/robots.txt", "headers": [{"key": "Content-Type", "value": "text/plain; charset=utf-8"}, {"key": "Cache-Control", "value": "public, max-age=3600, s-maxage=3600"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}, {"source": "/about", "destination": "/index.html"}, {"source": "/skills", "destination": "/index.html"}, {"source": "/projects", "destination": "/index.html"}, {"source": "/contact", "destination": "/index.html"}, {"source": "/((?!api|_next|_static|favicon.ico|sitemap.xml|robots.txt|logo|assets|images|projects|Resume).*)", "destination": "/index.html"}], "cleanUrls": true, "trailingSlash": false, "buildCommand": "npm run build", "outputDirectory": "dist", "functions": {"api/sitemap.js": {"maxDuration": 10}}}