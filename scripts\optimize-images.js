#!/usr/bin/env node

/**
 * Image Optimization Script
 * Optimizes profile.png and other large images for better performance
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Check if sharp is available (for image optimization)
let sharp;
try {
  sharp = (await import('sharp')).default;
} catch (error) {
  console.log('Sharp not available. Please install with: npm install sharp --save-dev');
  process.exit(1);
}

const PUBLIC_DIR = path.join(__dirname, '../public');
const PROFILE_IMAGE = path.join(PUBLIC_DIR, 'profile.png');
const OPTIMIZED_PROFILE = path.join(PUBLIC_DIR, 'profile-optimized.webp');
const OPTIMIZED_PROFILE_PNG = path.join(PUBLIC_DIR, 'profile-optimized.png');

async function optimizeProfileImage() {
  try {
    console.log('🖼️  Optimizing profile image...');
    
    if (!fs.existsSync(PROFILE_IMAGE)) {
      console.error('❌ profile.png not found');
      return;
    }

    // Get original file size
    const originalStats = fs.statSync(PROFILE_IMAGE);
    const originalSize = (originalStats.size / 1024).toFixed(2);
    console.log(`📊 Original size: ${originalSize}KB`);

    // Create optimized WebP version (best compression)
    await sharp(PROFILE_IMAGE)
      .resize(400, 400, { 
        fit: 'cover',
        position: 'center'
      })
      .webp({ 
        quality: 85,
        effort: 6
      })
      .toFile(OPTIMIZED_PROFILE);

    // Create optimized PNG fallback
    await sharp(PROFILE_IMAGE)
      .resize(400, 400, { 
        fit: 'cover',
        position: 'center'
      })
      .png({ 
        quality: 85,
        compressionLevel: 9,
        progressive: true
      })
      .toFile(OPTIMIZED_PROFILE_PNG);

    // Check new file sizes
    const webpStats = fs.statSync(OPTIMIZED_PROFILE);
    const pngStats = fs.statSync(OPTIMIZED_PROFILE_PNG);
    const webpSize = (webpStats.size / 1024).toFixed(2);
    const pngSize = (pngStats.size / 1024).toFixed(2);

    console.log(`✅ WebP optimized: ${webpSize}KB (${((1 - webpStats.size / originalStats.size) * 100).toFixed(1)}% reduction)`);
    console.log(`✅ PNG optimized: ${pngSize}KB (${((1 - pngStats.size / originalStats.size) * 100).toFixed(1)}% reduction)`);

    // Backup original and replace
    const backupPath = path.join(PUBLIC_DIR, 'profile-original.png');
    fs.copyFileSync(PROFILE_IMAGE, backupPath);
    fs.copyFileSync(OPTIMIZED_PROFILE_PNG, PROFILE_IMAGE);
    
    console.log('✅ Profile image optimized successfully!');
    console.log('📁 Original backed up as profile-original.png');
    console.log('🌐 WebP version available as profile-optimized.webp');

  } catch (error) {
    console.error('❌ Error optimizing profile image:', error.message);
  }
}

async function optimizeProjectImages() {
  try {
    console.log('🖼️  Optimizing project images...');
    
    const projectsDir = path.join(PUBLIC_DIR, 'projects');
    const files = fs.readdirSync(projectsDir);
    
    for (const file of files) {
      if (file.endsWith('.png')) {
        const filePath = path.join(projectsDir, file);
        const stats = fs.statSync(filePath);
        const sizeKB = (stats.size / 1024).toFixed(2);
        
        if (stats.size > 100 * 1024) { // If larger than 100KB
          console.log(`📊 Optimizing ${file} (${sizeKB}KB)...`);
          
          const optimizedPath = filePath.replace('.png', '-optimized.png');
          
          await sharp(filePath)
            .resize(800, 600, { 
              fit: 'inside',
              withoutEnlargement: true
            })
            .png({ 
              quality: 85,
              compressionLevel: 9,
              progressive: true
            })
            .toFile(optimizedPath);
            
          const optimizedStats = fs.statSync(optimizedPath);
          const optimizedSize = (optimizedStats.size / 1024).toFixed(2);
          
          console.log(`✅ ${file}: ${sizeKB}KB → ${optimizedSize}KB (${((1 - optimizedStats.size / stats.size) * 100).toFixed(1)}% reduction)`);
          
          // Replace original with optimized
          fs.copyFileSync(filePath, filePath.replace('.png', '-original.png'));
          fs.copyFileSync(optimizedPath, filePath);
          fs.unlinkSync(optimizedPath);
        }
      }
    }
    
    console.log('✅ Project images optimized successfully!');
    
  } catch (error) {
    console.error('❌ Error optimizing project images:', error.message);
  }
}

async function main() {
  console.log('🚀 Starting image optimization...\n');
  
  await optimizeProfileImage();
  console.log('');
  await optimizeProjectImages();
  
  console.log('\n🎉 Image optimization complete!');
  console.log('\n📋 Next steps:');
  console.log('1. Update LazyImage component to use WebP with PNG fallback');
  console.log('2. Test the optimized images');
  console.log('3. Run Lighthouse again to verify performance improvements');
}

// Run the main function
main().catch(console.error);
