'use client'
import React, { useState, useRef, useEffect, useMemo } from 'react'

interface LazyImageProps {
  src: string
  alt: string
  className?: string
  placeholder?: string
  onLoad?: () => void
  onError?: () => void
  enableWebP?: boolean
  priority?: boolean
  sizes?: string
  width?: number
  height?: number
}

// WebP support detection (cached)
let webpSupported: boolean | null = null
const supportsWebP = (): boolean => {
  if (webpSupported !== null) return webpSupported

  try {
    const canvas = document.createElement('canvas')
    canvas.width = 1
    canvas.height = 1
    webpSupported = canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0
  } catch {
    webpSupported = false
  }

  return webpSupported
}

const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  className = '',
  placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMTExMTExIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzY2NjY2NiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+',
  onLoad,
  onError,
  enableWebP = true,
  priority = false,
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  width,
  height
}) => {
  const [isLoaded, setIsLoaded] = useState(false)
  const [isError, setIsError] = useState(false)
  const [isInView, setIsInView] = useState(priority) // Load immediately if priority
  const imgRef = useRef<HTMLImageElement>(null)

  // Memoize optimized image sources
  const { imageSrc, webpSrc } = useMemo(() => {
    if (!src) return { imageSrc: '', webpSrc: '' }

    let optimizedSrc = src
    let optimizedWebpSrc = ''

    // Generate WebP source if enabled and supported
    if (enableWebP && supportsWebP()) {
      if (src.includes('profile.png')) {
        optimizedWebpSrc = '/profile-optimized.webp'
      } else if (src.includes('/projects/')) {
        // Generate WebP path for project images
        optimizedWebpSrc = src.replace('.png', '.webp')
      }
    }

    return { imageSrc: optimizedSrc, webpSrc: optimizedWebpSrc }
  }, [src, enableWebP])

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority) return // Skip observer for priority images

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true)
          observer.disconnect()
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px' // Reduced for better performance
      }
    )

    if (imgRef.current) {
      observer.observe(imgRef.current)
    }

    return () => observer.disconnect()
  }, [priority])

  const handleLoad = () => {
    setIsLoaded(true)
    onLoad?.()
  }

  const handleError = () => {
    console.warn(`Failed to load image: ${imageSrc}`)
    setIsError(true)
    onError?.()
  }

  // Determine loading strategy
  const shouldLoad = priority || isInView

  return (
    <div ref={imgRef} className={`relative overflow-hidden ${className}`}>
      {/* Optimized placeholder - only show if not loaded */}
      {!isLoaded && shouldLoad && (
        <div className="absolute inset-0 w-full h-full bg-mono-surface-dark/50 flex items-center justify-center">
          <div className="w-4 h-4 border border-mono-accent/30 border-t-mono-accent rounded-full animate-spin" />
        </div>
      )}

      {/* Actual image with WebP support and performance optimizations */}
      {shouldLoad && imageSrc && (
        <picture>
          {/* WebP source for supported browsers */}
          {webpSrc && (
            <source
              srcSet={webpSrc}
              type="image/webp"
              sizes={sizes}
            />
          )}
          {/* Fallback source */}
          <img
            src={imageSrc}
            alt={alt}
            className={`w-full h-full object-cover transition-opacity duration-300 ${
              isLoaded ? 'opacity-100' : 'opacity-0'
            }`}
            onLoad={handleLoad}
            onError={handleError}
            loading={priority ? 'eager' : 'lazy'}
            decoding="async"
            fetchpriority={priority ? 'high' : 'auto'}
            sizes={sizes}
            width={width}
            height={height}
            style={{
              contentVisibility: 'auto',
              containIntrinsicSize: width && height ? `${width}px ${height}px` : '400px 300px'
            }}
          />
        </picture>
      )}

      {/* Error state */}
      {isError && (
        <div className="absolute inset-0 flex items-center justify-center bg-mono-surface-dark text-mono-secondary text-sm">
          Failed to load image
        </div>
      )}
    </div>
  )
}

export default LazyImage
export { LazyImage }
