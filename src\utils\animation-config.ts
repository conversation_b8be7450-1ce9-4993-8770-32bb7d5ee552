/**
 * Performance-optimized animation configurations
 * Reduces animation overhead and improves performance
 */

import { Variants, Transition } from 'framer-motion'

// Check if user prefers reduced motion or is on a low-end device
export const prefersReducedMotion = () => {
  if (typeof window === 'undefined') return false

  // Check user preference
  const prefersReduced = window.matchMedia('(prefers-reduced-motion: reduce)').matches

  // Check for low-end device indicators
  const isLowEndDevice =
    // Low memory
    (navigator as any).deviceMemory && (navigator as any).deviceMemory < 4 ||
    // Slow connection
    (navigator as any).connection && (navigator as any).connection.effectiveType === 'slow-2g' ||
    (navigator as any).connection && (navigator as any).connection.effectiveType === '2g' ||
    // Mobile device with limited resources
    /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && window.innerWidth < 768

  return prefersReduced || isLowEndDevice
}

// Performance-optimized transition configurations
export const transitions = {
  // Ultra-fast for low-end devices
  instant: {
    duration: 0.1,
    ease: 'linear' as const
  } as Transition,

  // Fast transitions for better performance
  fast: {
    duration: prefersReducedMotion() ? 0.1 : 0.2,
    ease: [0.4, 0, 0.2, 1] as const
  } as Transition,

  // Standard transitions
  standard: {
    duration: prefersReducedMotion() ? 0.15 : 0.3,
    ease: [0.4, 0, 0.2, 1] as const
  } as Transition,

  // Smooth transitions for important elements (reduced on mobile)
  smooth: {
    duration: prefersReducedMotion() ? 0.2 : 0.4,
    ease: [0.4, 0, 0.2, 1] as const
  } as Transition,

  // Spring transitions (disabled on low-end devices)
  spring: prefersReducedMotion() ? {
    duration: 0.2,
    ease: [0.4, 0, 0.2, 1] as const
  } as Transition : {
    type: 'spring' as const,
    stiffness: 200,
    damping: 25
  } as Transition
}

// Optimized animation variants
export const fadeInUp: Variants = {
  hidden: { 
    opacity: 0, 
    y: 20,
    transition: transitions.fast
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: transitions.standard
  }
}

export const fadeIn: Variants = {
  hidden: { 
    opacity: 0,
    transition: transitions.fast
  },
  visible: { 
    opacity: 1,
    transition: transitions.standard
  }
}

export const scaleIn: Variants = {
  hidden: { 
    opacity: 0, 
    scale: 0.95,
    transition: transitions.fast
  },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: transitions.standard
  }
}

export const slideInLeft: Variants = {
  hidden: { 
    opacity: 0, 
    x: -30,
    transition: transitions.fast
  },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: transitions.standard
  }
}

export const slideInRight: Variants = {
  hidden: { 
    opacity: 0, 
    x: 30,
    transition: transitions.fast
  },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: transitions.standard
  }
}

// Container variants for staggered animations
export const staggerContainer: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
}

export const staggerItem: Variants = {
  hidden: { 
    opacity: 0, 
    y: 20 
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: transitions.standard
  }
}

// Hover animations (reduced for performance)
export const hoverScale = {
  scale: 1.02,
  transition: transitions.fast
}

export const hoverLift = {
  y: -4,
  transition: transitions.fast
}

// Viewport configuration for better performance
export const viewportConfig = {
  once: true,
  margin: "-10% 0px -10% 0px",
  amount: 0.3
}

// Performance-optimized motion props
export const motionProps = {
  // Reduce layout calculations
  layout: false,
  // Optimize for transform animations
  style: {
    willChange: 'transform, opacity'
  }
}

// Conditional animation based on reduced motion preference
export const getAnimation = (animation: Variants) => {
  return prefersReducedMotion() ? fadeIn : animation
}

// Conditional transition based on reduced motion preference
export const getTransition = (transition: Transition) => {
  return prefersReducedMotion() ? transitions.fast : transition
}

// Performance monitoring for animations
export const logAnimationPerformance = (componentName: string, startTime: number) => {
  if (process.env.NODE_ENV === 'development') {
    const endTime = performance.now()
    const duration = endTime - startTime
    
    if (duration > 16) { // 60fps threshold
      console.warn(`⚠️ Animation Performance: ${componentName} took ${duration.toFixed(2)}ms`)
    }
  }
}
